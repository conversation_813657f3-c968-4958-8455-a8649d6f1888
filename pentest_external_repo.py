#!/usr/bin/env python3
"""
security_mvp/pentest_external_repo.py

Complete penetration testing workflow for external repositories.
Clones repo -> Builds Docker container -> Runs app -> AI agents attack -> Cleanup
"""

import argparse
import os
import sys
import tempfile
import shutil
import subprocess
import json
import time
import signal
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional

from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich import print as rprint

console = Console()

class PentestOrchestrator:
    """Orchestrates the complete penetration testing workflow."""
    
    def __init__(self, repo_url: str, results_dir: str, keep_artifacts: bool = False):
        self.repo_url = repo_url
        self.results_dir = results_dir
        self.keep_artifacts = keep_artifacts
        self.container_name = f"pentest-target-{int(time.time())}"
        self.container_port = 3000
        self.host_port = self._find_available_port()
        self.temp_dir = None
        self.repo_dir = None
        
    def _find_available_port(self, start_port: int = 8080) -> int:
        """Find an available port for the container."""
        import socket
        for port in range(start_port, start_port + 100):
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                try:
                    s.bind(('localhost', port))
                    return port
                except OSError:
                    continue
        raise RuntimeError("No available ports found")
    
    def run_command(self, cmd: list, cwd: str = None, timeout: int = 300) -> Optional[subprocess.CompletedProcess]:
        """Run a command safely with timeout."""
        try:
            console.print(f"[dim]Running: {' '.join(cmd)}[/dim]")
            result = subprocess.run(
                cmd, 
                cwd=cwd, 
                capture_output=True, 
                text=True, 
                timeout=timeout,
                check=False
            )
            return result
        except subprocess.TimeoutExpired:
            console.print(f"[red]⏰ Command timed out: {' '.join(cmd)}[/red]")
            return None
        except Exception as e:
            console.print(f"[red]❌ Command failed: {e}[/red]")
            return None
    
    def validate_repo_url(self) -> bool:
        """Validate that the repository URL is safe to clone."""
        allowed_domains = ["github.com", "gitlab.com", "bitbucket.org"]
        
        if not self.repo_url.startswith(("https://", "http://")):
            console.print("[red]❌ Only HTTP/HTTPS URLs are allowed[/red]")
            return False
        
        for domain in allowed_domains:
            if domain in self.repo_url:
                return True
        
        console.print(f"[red]❌ Repository domain not in allowed list: {allowed_domains}[/red]")
        return False
    
    def clone_repository(self) -> bool:
        """Clone repository into temporary directory."""
        console.print(f"[yellow]📥 Cloning repository: {self.repo_url}[/yellow]")
        
        self.temp_dir = tempfile.mkdtemp(prefix="pentest_")
        repo_name = self.repo_url.split('/')[-1].replace('.git', '')
        self.repo_dir = os.path.join(self.temp_dir, repo_name)
        
        cmd = ["git", "clone", "--depth", "1", self.repo_url, self.repo_dir]
        result = self.run_command(cmd, timeout=120)
        
        if result and result.returncode == 0:
            console.print("[green]✅ Repository cloned successfully[/green]")
            return True
        else:
            console.print(f"[red]❌ Failed to clone repository[/red]")
            if result:
                console.print(f"[red]Error: {result.stderr}[/red]")
            return False
    
    def detect_app_type(self) -> str:
        """Detect the type of application to determine how to run it."""
        if os.path.exists(os.path.join(self.repo_dir, "package.json")):
            return "nodejs"
        elif os.path.exists(os.path.join(self.repo_dir, "requirements.txt")):
            return "python"
        elif os.path.exists(os.path.join(self.repo_dir, "Dockerfile")):
            return "docker"
        else:
            return "unknown"
    
    def build_container(self) -> bool:
        """Build Docker container for the target application."""
        console.print("[yellow]🔨 Building Docker container for target application...[/yellow]")
        
        app_type = self.detect_app_type()
        console.print(f"[cyan]📋 Detected application type: {app_type}[/cyan]")
        
        # Find the actual application directory
        app_dir = self.repo_dir
        if os.path.exists(os.path.join(self.repo_dir, "moon-event-center")):
            app_dir = os.path.join(self.repo_dir, "moon-event-center")
        
        # Copy our Dockerfile to the app directory
        dockerfile_src = os.path.join(os.getcwd(), "Dockerfile.target-app")
        dockerfile_dst = os.path.join(app_dir, "Dockerfile")
        shutil.copy2(dockerfile_src, dockerfile_dst)
        
        # Build the container
        cmd = ["docker", "build", "-t", self.container_name, "."]
        result = self.run_command(cmd, cwd=app_dir, timeout=600)
        
        if result and result.returncode == 0:
            console.print("[green]✅ Docker container built successfully[/green]")
            return True
        else:
            console.print(f"[red]❌ Failed to build Docker container[/red]")
            if result:
                console.print(f"[red]Error: {result.stderr}[/red]")
            return False
    
    def start_container(self) -> bool:
        """Start the Docker container with the target application."""
        console.print(f"[yellow]🚀 Starting container on port {self.host_port}...[/yellow]")
        
        cmd = [
            "docker", "run", "-d",
            "--name", self.container_name,
            "-p", f"{self.host_port}:{self.container_port}",
            self.container_name
        ]
        
        result = self.run_command(cmd, timeout=60)
        
        if result and result.returncode == 0:
            console.print(f"[green]✅ Container started successfully[/green]")
            console.print(f"[cyan]🌐 Application available at: http://localhost:{self.host_port}[/cyan]")
            
            # Wait for the application to be ready
            return self._wait_for_app_ready()
        else:
            console.print(f"[red]❌ Failed to start container[/red]")
            if result:
                console.print(f"[red]Error: {result.stderr}[/red]")
            return False
    
    def _wait_for_app_ready(self, max_wait: int = 60) -> bool:
        """Wait for the application to be ready to receive requests."""
        console.print("[yellow]⏳ Waiting for application to be ready...[/yellow]")

        import requests
        target_url = f"http://localhost:{self.host_port}"

        for i in range(max_wait):
            try:
                response = requests.get(target_url, timeout=5)
                if response.status_code == 200:
                    console.print("[green]✅ Application is ready for testing[/green]")
                    return True
            except requests.exceptions.RequestException as e:
                if i % 10 == 0:
                    console.print(f"[dim]Connection attempt failed: {e}[/dim]")

            # Check container logs every 10 seconds
            if i % 10 == 0 and i > 0:
                console.print(f"[dim]Checking container logs...[/dim]")
                logs_result = self.run_command(["docker", "logs", "--tail", "10", self.container_name], timeout=10)
                if logs_result and logs_result.stdout:
                    console.print(f"[dim]Container logs: {logs_result.stdout[-200:]}[/dim]")
                if logs_result and logs_result.stderr:
                    console.print(f"[dim]Container errors: {logs_result.stderr[-200:]}[/dim]")

            time.sleep(1)
            if i % 10 == 0:
                console.print(f"[dim]Still waiting... ({i}/{max_wait}s)[/dim]")

        console.print("[red]❌ Application failed to become ready[/red]")
        # Show final logs
        console.print("[yellow]Final container logs:[/yellow]")
        logs_result = self.run_command(["docker", "logs", self.container_name], timeout=10)
        if logs_result:
            console.print(f"[dim]STDOUT: {logs_result.stdout}[/dim]")
            console.print(f"[dim]STDERR: {logs_result.stderr}[/dim]")
        return False
    
    def run_ai_penetration_test(self) -> Dict[str, Any]:
        """Run AI agents to perform penetration testing on the live application."""
        console.print("[yellow]🤖 Starting AI penetration testing...[/yellow]")
        
        target_url = f"http://localhost:{self.host_port}"
        
        try:
            # Import AI agents
            from ai_agents_simple import SimpleSecurityOrchestrator
            from ai_config import AIConfigManager
            
            # Initialize AI configuration
            ai_config = AIConfigManager()
            if not ai_config.test_connection():
                console.print("[red]❌ OpenRouter API connection failed. Check your API key.[/red]")
                return {"status": "failed", "error": "API connection failed"}
            
            # Create orchestrator
            orchestrator = SimpleSecurityOrchestrator()
            
            # Prepare scan context for live testing
            scan_context = {
                "target_url": target_url,
                "application_type": self.detect_app_type(),
                "testing_mode": "live_penetration_test",
                "repository": self.repo_url,
                "container_info": {
                    "name": self.container_name,
                    "port": self.host_port
                }
            }
            
            console.print(f"[cyan]🎯 Target URL: {target_url}[/cyan]")
            console.print("[cyan]🔥 AI agents will perform ACTIVE penetration testing[/cyan]")
            
            # Run AI-powered penetration testing
            ai_results = orchestrator.run_security_assessment(target_url, scan_context)
            
            # Save results
            os.makedirs(self.results_dir, exist_ok=True)
            results_file = os.path.join(self.results_dir, 'ai_penetration_test.md')
            with open(results_file, 'w', encoding='utf-8') as f:
                f.write("# AI Penetration Testing Report\n\n")
                f.write(f"**Target:** {target_url}\n")
                f.write(f"**Repository:** {self.repo_url}\n")
                f.write(f"**Test Date:** {datetime.now().isoformat()}\n\n")
                f.write("---\n\n")
                
                for step, result in ai_results.items():
                    f.write(f"## {step.replace('_', ' ').title()}\n\n")
                    f.write(f"{result}\n\n")
                    f.write("---\n\n")
            
            console.print(f"[green]✅ AI penetration testing completed[/green]")
            console.print(f"[cyan]📄 Results saved to: {results_file}[/cyan]")
            
            return {"status": "success", "results_file": results_file, "findings": ai_results}
            
        except ImportError as e:
            error_msg = f"Failed to import AI modules: {e}"
            console.print(f"[red]❌ {error_msg}[/red]")
            return {"status": "failed", "error": error_msg}
        except Exception as e:
            error_msg = f"AI penetration testing failed: {e}"
            console.print(f"[red]❌ {error_msg}[/red]")
            return {"status": "failed", "error": error_msg}
    
    def cleanup(self):
        """Clean up containers and temporary files."""
        console.print("[yellow]🧹 Cleaning up test environment...[/yellow]")
        
        # Stop and remove container
        try:
            self.run_command(["docker", "stop", self.container_name], timeout=30)
            self.run_command(["docker", "rm", self.container_name], timeout=30)
            self.run_command(["docker", "rmi", self.container_name], timeout=60)
            console.print("[green]✅ Docker container cleaned up[/green]")
        except Exception as e:
            console.print(f"[yellow]⚠️ Container cleanup warning: {e}[/yellow]")
        
        # Clean up temporary directory
        if self.temp_dir and os.path.exists(self.temp_dir) and not self.keep_artifacts:
            try:
                shutil.rmtree(self.temp_dir)
                console.print("[green]✅ Temporary files cleaned up[/green]")
            except Exception as e:
                console.print(f"[yellow]⚠️ Temp cleanup warning: {e}[/yellow]")
        elif self.keep_artifacts:
            console.print(f"[cyan]📁 Artifacts preserved in: {self.temp_dir}[/cyan]")
    
    def run_complete_pentest(self) -> Dict[str, Any]:
        """Run the complete penetration testing workflow."""
        console.print(Panel.fit(
            "[bold red]🔥 ACTIVE PENETRATION TESTING 🔥[/bold red]\n\n"
            "[yellow]This will perform REAL attacks on a running application![/yellow]\n"
            "[cyan]The application will be isolated in Docker for safety.[/cyan]",
            border_style="red"
        ))
        
        try:
            # Validate repository
            if not self.validate_repo_url():
                return {"status": "failed", "error": "Invalid repository URL"}
            
            # Clone repository
            if not self.clone_repository():
                return {"status": "failed", "error": "Failed to clone repository"}
            
            # Build container
            if not self.build_container():
                return {"status": "failed", "error": "Failed to build container"}
            
            # Start container
            if not self.start_container():
                return {"status": "failed", "error": "Failed to start container"}
            
            # Run AI penetration testing
            pentest_results = self.run_ai_penetration_test()
            
            return pentest_results
            
        except KeyboardInterrupt:
            console.print("\n[red]❌ Penetration test interrupted by user[/red]")
            return {"status": "interrupted"}
        except Exception as e:
            console.print(f"[red]❌ Unexpected error: {e}[/red]")
            return {"status": "failed", "error": str(e)}
        finally:
            # Always cleanup
            self.cleanup()


def main():
    parser = argparse.ArgumentParser(
        description="🔥 AI-Powered Penetration Testing for External Repositories",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Run penetration test on Moon Event Center
  python pentest_external_repo.py https://github.com/PapaBear1981/Moon-Event-Center-Colab.git
  
  # Keep artifacts for analysis
  python pentest_external_repo.py https://github.com/user/repo.git --keep-artifacts
        """
    )
    
    parser.add_argument(
        "repo_url",
        help="Repository URL to clone and penetration test"
    )
    
    parser.add_argument(
        "--results", "-r",
        help="Results directory (default: pentest_TIMESTAMP)",
        default=f"pentest_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    )
    
    parser.add_argument(
        "--keep-artifacts",
        action="store_true",
        help="Keep Docker images and temporary files after testing"
    )
    
    args = parser.parse_args()
    
    # Create orchestrator and run penetration test
    orchestrator = PentestOrchestrator(
        repo_url=args.repo_url,
        results_dir=args.results,
        keep_artifacts=args.keep_artifacts
    )
    
    # Handle Ctrl+C gracefully
    def signal_handler(sig, frame):
        console.print("\n[yellow]⚠️ Received interrupt signal, cleaning up...[/yellow]")
        orchestrator.cleanup()
        sys.exit(1)
    
    signal.signal(signal.SIGINT, signal_handler)
    
    # Run the complete penetration test
    result = orchestrator.run_complete_pentest()
    
    # Display final results
    if result["status"] == "success":
        console.print(Panel.fit(
            f"[bold green]🎉 Penetration Testing Completed![/bold green]\n\n"
            f"[cyan]Repository: {args.repo_url}[/cyan]\n"
            f"[cyan]Results: {args.results}[/cyan]\n"
            f"[cyan]Report: {result.get('results_file', 'N/A')}[/cyan]",
            border_style="green"
        ))
        sys.exit(0)
    else:
        console.print(Panel.fit(
            f"[bold red]❌ Penetration Testing Failed[/bold red]\n\n"
            f"[red]Error: {result.get('error', 'Unknown error')}[/red]",
            border_style="red"
        ))
        sys.exit(1)


if __name__ == "__main__":
    main()
